import {createNativeStackNavigator} from '@react-navigation/native-stack';

import Instructors from '../../../../modules/customer/listview/instructors';
import SettingProfile from '../../../../modules/customer/setting/setting';
import {navigateReset, RootScreen} from '../../../../router/router';
import {useDispatch} from 'react-redux';
import {useEffect} from 'react';
import LoginScreen from '../../../../modules/customer/login';
import {CustomerActions} from '../../../../redux/reducers/CustomerReducer';
import {
  getDataToAsyncStorage,
  saveDataToAsyncStorage,
} from '../../../../utils/AsyncStorage';
import {SplashScreen} from '../../Splash';
import BiometricSetting from '../../../../modules/customer/setting/biometricSetting';
import NotificationIndex from '../../../../modules/notification/view';
import VnpayPaymentScreen from '../../../../utils/vnpayWebview';
import ForgotPass from '../../../../modules/customer/form/forgot-pass';
import FAQView from '../../../../modules/customer/listview/FAQView';
import IntroPage from '../../../Page/Intro';
import EComLayout from '../../mainLayout';
import ProductDetail from '../../../../modules/Product/productDetail';
import DetailPost from '../../../../modules/news/detail';
import HotProductsDemo from '../../../Page/HotProductsDemo';

const Eschool = createNativeStackNavigator();

export function EComStackNavigator() {
  return (
    <Eschool.Navigator
      screenOptions={{headerShown: false, orientation: 'portrait'}}>
      <Eschool.Screen
        name={RootScreen.splashView}
        component={SplashScreenWithAuthCheck}
      />
      <Eschool.Screen
        name={RootScreen.login}
        component={LoginScreen}
        options={{animation: 'fade'}}
      />
      <Eschool.Screen
        name={RootScreen.navigateEComView}
        component={EComLayout}
      />
      <Eschool.Screen name={RootScreen.Instructors} component={Instructors} />
      <Eschool.Screen
        name={RootScreen.Notification}
        component={NotificationIndex}
      />
      <Eschool.Screen
        name={RootScreen.SettingProfile}
        component={SettingProfile}
      />
      <Eschool.Screen
        name={RootScreen.ProductDetail}
        component={ProductDetail}
      />
      <Eschool.Screen name={RootScreen.DetailPost} component={DetailPost} />
      <Eschool.Screen
        name={RootScreen.CartPage}
        component={require('../../../Page/CartPage').default}
      />
      <Eschool.Screen
        name={RootScreen.CheckoutPage}
        component={require('../../../Page/CheckoutPage').default}
      />
      <Eschool.Screen
        name={RootScreen.BiometricSetting}
        component={BiometricSetting}
      />
      <Eschool.Screen name={RootScreen.ForgotPass} component={ForgotPass} />
      <Eschool.Screen
        name={RootScreen.VnpayPaymentScreen}
        component={VnpayPaymentScreen}
      />
      <Eschool.Screen name={RootScreen.Intro} component={IntroPage} />
      <Eschool.Screen
        name={RootScreen.OrderDetailPage}
        component={require('../../../Page/OrderDetailPage').default}
      />
      <Eschool.Screen
        name={RootScreen.HotProductsDemo}
        component={HotProductsDemo}
      />
      <Eschool.Screen
        name={RootScreen.AllHotProductsPage}
        component={require('../../../Page/AllHotProductsPage').default}
      />
    </Eschool.Navigator>
  );
}

const SplashScreenWithAuthCheck = ({navigation}: any) => {
  const dispatch = useDispatch<any>();
  useEffect(() => {
    const checkAuthAndNavigate = async () => {
      try {
        // Wait for a minimum of 3 seconds for splash screen
        const splashTimer = new Promise(resolve => setTimeout(resolve, 3000));

        // Check for user token
        const tokenCheck = await getDataToAsyncStorage('accessToken');

        // Wait for both operations to complete
        const [_, accessToken] = await Promise.all([splashTimer, tokenCheck]);

        // check the first time open app and show IntroPage
        const isFirstTime = await getDataToAsyncStorage('isFirstTime');
        if (!isFirstTime) {
          await saveDataToAsyncStorage('isFirstTime', 'true');
          navigateReset(RootScreen.Intro);
          return;
        }

        // get info
        if (accessToken) {
          dispatch(CustomerActions.getInfor());
        }
        const nextRoute = accessToken
          ? RootScreen.navigateEComView
          : RootScreen.login;

        navigateReset(nextRoute);
      } catch (error) {
        console.error('Authentication check error:', error);
        // Default to Auth flow if error occurs
        navigateReset(RootScreen.login);
      }
    };

    checkAuthAndNavigate();
  }, [navigation, dispatch]);

  return <SplashScreen />;
};
